"""
翻译API - LangGraph agent with direct result passthrough
"""
from fastapi import APIRouter
from fastapi.responses import JSONResponse, StreamingResponse

from app.agents.base import agent_registry
from app.api.models import TranslationRequest
from app.core.models import AgentType
from app.utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter()


@router.post("/translate", 
             summary="Text Translation",
             description="Translate text using multiple providers (Doubao or LLM); supports streaming and non-streaming modes.")
async def translate_text(request: TranslationRequest):
    """Text translation with LangGraph processing and direct result passthrough"""
    # 获取 provider，优先从 translateOptions 中获取，如果没有则使用默认值
    provider = "llm_translate"  # 默认值
    if request.translateOptions and hasattr(request.translateOptions, 'provider'):
        provider = request.translateOptions.provider

    # 计算文本数量（兼容字符串和数组格式）
    text_count = 1 if isinstance(request.question, str) else len(request.question)
    logger.info(f"Processing translation request:request :{request} provider={provider}, texts={text_count}, stream={request.stream}")

    # Create translation agent
    agent = agent_registry.create_agent(AgentType.TRANSLATION)

    # Convert request to agent input format
    translate_options = {}
    model = None  # 提取 model 字段作为顶级参数

    if request.translateOptions:
        translate_options = {
            "src_lang": request.translateOptions.src_lang,
            "tgt_lang": request.translateOptions.tgt_lang,
            "provider": request.translateOptions.provider
        }
        model = request.model  # 提取 model 字段
        logger.info(f"API层提取到的model参数: {model}")
        logger.info(f"完整的translateOptions: {request.translateOptions}")
    else:
        # 如果没有 translateOptions，使用默认值
        translate_options = {
            "src_lang": "en",
            "tgt_lang": "zh",
            "provider": provider
        }
        logger.info(f"使用默认translateOptions，model为None")

    # Use agent's direct processing method
    result = await agent.process_translation(
        question=request.question,
        stream=request.stream,
        translate_options=translate_options,
        model=model,  # 将 model 作为顶级参数传递
        provider=provider  # 保持向后兼容
    )

    if request.stream:
        # Return raw stream
        async def stream_generator():
            async for line in result:
                yield line
        
        return StreamingResponse(
            stream_generator(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "*"
            }
        )
    else:
        # Return raw result
        return JSONResponse(status_code=200, content=result)
