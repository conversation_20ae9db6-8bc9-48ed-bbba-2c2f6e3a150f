"""
翻译服务 - 支持多种翻译API提供商
"""
import aiohttp
import asyncio
import json
import time
from typing import Dict, Any, Optional, List, AsyncGenerator, Union
from dataclasses import dataclass
from enum import Enum

from app.config import settings
from app.services.llm_service import llm_service
from app.agents.translation.prompts import build_translation_messages
from app.utils.logger import get_logger

logger = get_logger(__name__)



class TranslationProvider(str, Enum):
    """翻译服务提供商"""
    DOUBAO = "doubao"
    LLM_TRANSLATE = "llm_translate"


@dataclass
class TranslationRequest:
    """翻译请求数据结构"""
    question: Union[str, List[Dict[str, str]]]  # 支持字符串或数组格式
    stream: bool = False
    translate_options: Dict[str, str] = None  # {"src_lang": "en", "tgt_lang": "zh", "provider": "llm_translate", "model": "ht::saas-deepseek-v3"}
    model: Optional[str] = None  # "ht::saas-deepseek-v3"
    _is_string_input: bool = False  # 内部标记，用于标识原始输入格式


@dataclass
class TranslationResult:
    """翻译结果数据结构"""
    code: str
    message: str
    data: List[Dict[str, str]]  # [{"sourceText": "hello", "text": "你好"}]


class DoubaoTranslationClient:
    """Doubao翻译API客户端"""

    def __init__(self):
        self.api_url = settings.doubao_translation_api_url
        self.headers = {
            "appSysId": settings.doubao_app_sys_id,
            "token": settings.doubao_token,
            "Content-Type": "application/json"
        }
        self.session = None
        self._timeout = aiohttp.ClientTimeout(total=180)  # 3分钟超时

    async def _get_session(self) -> aiohttp.ClientSession:
        """获取HTTP会话"""
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession(timeout=self._timeout)
        return self.session

    def _build_payload(self, request: TranslationRequest) -> Dict[str, Any]:
        """构建Doubao API请求载荷"""
        # 根据输入格式提取文本
        if isinstance(request.question, str):
            # 字符串输入
            text_list = [request.question]
        else:
            # 数组输入：从question数组中提取所有sourceText值
            text_list = [item["sourceText"] for item in request.question]

        translate_options = request.translate_options or {}

        return {
            "data": {
                "text": text_list,
                "src_lang": translate_options.get("src_lang", "en"),
                "tgt_lang": translate_options.get("tgt_lang", "zh"),
                "model_name": "doubao"
            }
        }

    async def translate(self, request: TranslationRequest) -> TranslationResult:
        """执行Doubao翻译"""
        session = await self._get_session()
        payload = self._build_payload(request)
        
        logger.info(f"=== Doubao翻译请求调试信息 ===")
        logger.info(f"URL: {self.api_url}")
        logger.info(f"Headers: {self.headers}")
        logger.info(f"Payload: {json.dumps(payload, ensure_ascii=False, indent=2)}")
        
        try:
            # 计算文本数量（兼容字符串和数组格式）
            text_count = 1 if isinstance(request.question, str) else len(request.question)
            logger.info(f"开始执行Doubao翻译，文本数量: {text_count}")
            
            async with session.post(
                self.api_url,
                headers=self.headers,
                json=payload
            ) as response:
                logger.info(f"=== Doubao翻译响应调试信息 ===")
                logger.info(f"response.status: {response.status}")
                logger.info(f"response.headers: {dict(response.headers)}")
                
                response_text = await response.text()
                logger.info(f"response_text: {response_text}")
                
                if response.status == 200:
                    try:
                        result = json.loads(response_text)
                        logger.info(f"JSON successed...")
                        parsed_result = self._parse_doubao_result(result, request.question)
                        logger.info(f"Doubao finished: code={parsed_result.code}")
                        return parsed_result
                    except json.JSONDecodeError as e:
                        logger.error(f"JSON解析失败: {str(e)}")
                        logger.error(f"原始响应: {response_text}")
                        return TranslationResult(
                            code="error",
                            message=f"响应解析失败: {str(e)}",
                            data=[]
                        )
                else:
                    logger.error(f"Doubao翻译请求失败: {response.status}")
                    logger.error(f"错误内容: {response_text}")
                    return TranslationResult(
                        code="error",
                        message=f"请求失败: HTTP {response.status}",
                        data=[]
                    )
                    
        except Exception as e:
            logger.error(f"Doubao翻译执行异常: {str(e)}", exc_info=True)
            return TranslationResult(
                code="error",
                message=f"翻译异常: {str(e)}",
                data=[]
            )

    def _parse_doubao_result(self, response: Dict[str, Any], original_question: Union[str, List[Dict[str, str]]]) -> TranslationResult:
        """解析Doubao API响应为标准格式"""
        logger.info(f"=== 开始解析Doubao翻译结果 ===")
        logger.info(f"响应类型: {type(response)}")
        logger.info(f"响应键: {list(response.keys()) if isinstance(response, dict) else 'Not a dict'}")

        try:
            # 根据实际Doubao API响应格式解析
            # 实际格式: {"code": 200, "traceId": -1, "result": ["翻译结果1", "翻译结果2"]}
            if "code" in response and response["code"] == 200:
                # 获取翻译结果数组
                translated_texts = response.get("result", [])
                logger.info(f"Doubao API result: {translated_texts} original_question {original_question}")

                # 根据输入格式构建结果数据
                result_data = []

                if isinstance(original_question, str):
                    # 字符串输入：处理单个翻译结果
                    translated_text = translated_texts[0] if translated_texts else original_question
                    result_data.append({
                        "sourceText": original_question,
                        "text": translated_text
                    })
                    logger.info(f"字符串输入处理完成: '{original_question}' -> '{translated_text}'")
                else:
                    # 数组输入：保持原有逻辑
                    for i, question in enumerate(original_question):
                        translated_text = translated_texts[i] if i < len(translated_texts) else question["sourceText"]
                        result_data.append({
                            "sourceText": question["sourceText"],
                            "text": translated_text
                        })
                    logger.info(f"数组输入处理完成: {len(original_question)} 个文本")

                logger.info(f"解析完成，返回 {len(result_data)} 个翻译结果")
                return TranslationResult(
                    code="success",
                    message="翻译成功",
                    data=result_data
                )
            else:
                # 处理错误响应
                error_code = response.get("code", "unknown")
                error_message = response.get("message", f"Doubao API返回错误码: {error_code}")
                logger.warning(f"Doubao API返回错误: code={error_code}, message={error_message}")
                logger.warning(f"完整响应: {response}")

                return TranslationResult(
                    code="error",
                    message=error_message,
                    data=[]
                )
                        
        except Exception as e:
            logger.error(f"解析Doubao翻译结果失败: {str(e)}", exc_info=True)
            logger.error(f"响应内容: {json.dumps(response, ensure_ascii=False, indent=2)}")
            return TranslationResult(
                code="error",
                message=f"结果解析异常: {str(e)}",
                data=[]
            )

    async def close(self):
        """关闭会话"""
        if self.session and not self.session.closed:
            await self.session.close()
            self.session = None


class LLMTranslationClient:
    """基于LLM的翻译客户端"""

    async def translate(self, request: TranslationRequest) -> TranslationResult:
        """执行LLM翻译"""
        try:
            translate_options = request.translate_options or {}
            src_lang = translate_options.get("src_lang", "en")
            tgt_lang = translate_options.get("tgt_lang", "zh")
            # 获取用户指定的模型，如果没有指定则使用默认模型
            model = request.model or settings.default_llm_model

            logger.info(f"LLM非流式翻译: request.model={request.model}, 最终使用模型={model}")

            # 验证模型是否可用（如果指定了模型）
            if request.model is not None:
                from app.services.llm_service import llm_service
                if not llm_service.validate_model(model):
                    logger.warning(f"指定的模型 {model} 不可用，使用默认模型 {settings.default_llm_model}")
                    model = settings.default_llm_model

            result_data = []

            # 根据输入格式处理翻译
            if isinstance(request.question, str):
                # 字符串输入：直接翻译
                text = request.question
                messages = build_translation_messages(text, src_lang, tgt_lang)

                # 调用LLM服务
                from app.services.llm_service import llm_service
                response = await llm_service.chat_completion(
                    messages=messages,
                    model=model,
                    stream=False
                )

                translated_text = response.choices[0].message.content.strip()
                result_data.append({
                    "sourceText": text,
                    "text": translated_text
                })

                logger.info(f"字符串翻译完成: {text[:50]}... -> {translated_text[:50]}...")
            else:
                # 数组输入：逐个翻译每个文本
                for question_item in request.question:
                    text = question_item["sourceText"]

                    # 构建翻译消息
                    messages = build_translation_messages(text, src_lang, tgt_lang)

                    # 调用LLM服务，使用指定的模型
                    from app.services.llm_service import llm_service
                    response = await llm_service.chat_completion(
                        messages=messages,
                        model=model,
                        max_tokens=len(text) * 3,  # 动态调整token数量
                        temperature=0.3
                    )

                    # 提取翻译结果
                    translated_text = self._extract_translation(response)

                    result_data.append({
                        "sourceText": text,
                        "text": translated_text
                    })

                    logger.info(f"数组翻译完成: {text[:50]}... -> {translated_text[:50]}...")

            return TranslationResult(
                code="success",
                message="翻译成功",
                data=result_data
            )

        except Exception as e:
            logger.error(f"LLM翻译失败: {str(e)}", exc_info=True)
            return TranslationResult(
                code="error",
                message=f"LLM翻译异常: {str(e)}",
                data=[]
            )

    async def stream_translate(self, request: TranslationRequest) -> AsyncGenerator[str, None]:
        """执行LLM流式翻译 - 支持并发处理和标准化输出格式"""
        try:
            translate_options = request.translate_options or {}
            src_lang = translate_options.get("src_lang", "en")
            tgt_lang = translate_options.get("tgt_lang", "zh")
            # 获取用户指定的模型，如果没有指定则使用默认模型
            model = request.model or settings.default_llm_model

            logger.info(f"start handle translate with llm_translate，request is {request}, the model is: {model}")

            # 获取输入格式信息
            is_string_input = getattr(request, '_is_string_input', False)

            logger.info(f"is_string_input {is_string_input}")

            # 根据输入格式选择处理方式
            if is_string_input:
                # 字符串输入：直接处理单个文本
                logger.info(f"处理字符串输入: {request.question}")
                async for chunk in self._stream_translate_single_text(
                    text=request.question,
                    index=0,
                    src_lang=src_lang,
                    tgt_lang=tgt_lang,
                    model=model,
                    is_string_input=True
                ):
                    yield chunk
            else:
                # 数组输入：创建并发翻译任务
                tasks = []
                for idx, question_item in enumerate(request.question):
                    task = self._stream_translate_single_text(
                        text=question_item["sourceText"],
                        index=idx,
                        src_lang=src_lang,
                        tgt_lang=tgt_lang,
                        model=model,
                        is_string_input=False
                    )
                    tasks.append(task)

                # 使用异步生成器合并多个流式输出
                async def merge_streams():
                    """合并多个流式输出"""
                    # 创建队列收集所有流式数据
                    output_queue = asyncio.Queue()
                    active_tasks = set()

                    # 为每个任务创建收集器
                    async def collect_from_stream(stream_gen, task_id):
                        try:
                            async for chunk in stream_gen:
                                await output_queue.put((task_id, chunk))
                        except Exception as e:
                            logger.error(f"收集流式数据失败 (任务 {task_id}): {str(e)}")
                        finally:
                            active_tasks.discard(task_id)
                            if not active_tasks:
                                await output_queue.put((None, None))  # 完成信号

                    # 启动所有收集器
                    for i, task in enumerate(tasks):
                        active_tasks.add(i)
                        asyncio.create_task(collect_from_stream(task, i))

                    # 输出合并的流式数据
                    while True:
                        try:
                            task_id, chunk = await asyncio.wait_for(output_queue.get(), timeout=60.0)
                            if task_id is None:  # 完成信号
                                break
                            yield chunk
                        except asyncio.TimeoutError:
                            logger.warning("流式翻译合并超时")
                            break
                        except Exception as e:
                            logger.error(f"流式数据合并异常: {str(e)}")
                            break

                # 输出合并后的流式数据
                async for chunk in merge_streams():
                    yield chunk

                # 发送最终完成响应
                yield "data: [DONE]\n\n"
                logger.info("并发流式翻译完成")

        except Exception as e:
            logger.error(f"LLM流式翻译失败: {str(e)}", exc_info=True)
            error_data = {
                "error": True,
                "message": f"LLM流式翻译异常: {str(e)}"
            }

            error_response = {
                "choices": [
                    {
                        "delta": {
                            "content": json.dumps(error_data, ensure_ascii=False),
                            "role": "assistant"
                        },
                        "index": 0
                    }
                ],
                "created": int(time.time()),
                "id": f"llm-error-{hash(str(e))}",
                "model": settings.default_llm_model,
                "service_tier": "default",
                "object": "chat.completion.chunk",
                "usage": None
            }
            yield f"data: {json.dumps(error_response, ensure_ascii=False)}\n\n"
            yield "data: [DONE]\n\n"

    async def _stream_translate_single_text(self, text: str, index: int, src_lang: str, tgt_lang: str, model: str = None, is_string_input: bool = False) -> AsyncGenerator[str, None]:
        """流式翻译单个文本 - 使用标准化增量格式"""
        try:
            # 如果没有指定模型，使用默认模型
            if model is None:
                model = settings.default_llm_model

            logger.info(f"start _stream_translate_single_text is_string_input :{is_string_input}")

            # 构建翻译消息
            messages = build_translation_messages(text, src_lang, tgt_lang)

            # 调用LLM流式服务，使用指定的模型
            stream_generator = llm_service.stream_chat_completion(
                messages=messages,
                model=model,
                max_tokens=len(text) * 3,
                temperature=0.3
            )

            # 处理流式响应
            accumulated_content = ""
            async for chunk in stream_generator:
                try:
                    # 处理不同格式的SSE数据
                    chunk_data = chunk.strip()

                    # 如果包含"data: "前缀，移除它
                    if chunk_data.startswith("data: "):
                        chunk_data = chunk_data[6:]

                    # 跳过空行和结束标记
                    if not chunk_data or chunk_data == "[DONE]":
                        if chunk_data == "[DONE]":
                            break
                        continue

                    # 解析JSON数据
                    chunk_json = json.loads(chunk_data)

                    # 提取增量内容
                    if "choices" in chunk_json and chunk_json["choices"]:
                        choice = chunk_json["choices"][0]
                        if "delta" in choice and "content" in choice["delta"]:
                            delta_content = choice["delta"]["content"]
                            if delta_content:
                                accumulated_content = delta_content

                                # 根据输入格式调整content输出
                                if is_string_input:
                                    # 字符串输入：直接输出翻译文本
                                    content = accumulated_content
                                else:
                                    # 数组输入：保持JSON格式
                                    content_data = {
                                        "index": index,
                                        "sourceText": text,
                                        "text": accumulated_content
                                    }
                                    content = json.dumps(content_data, ensure_ascii=False)

                                unified_response = {
                                    "choices": [
                                        {
                                            "delta": {
                                                "content": content,
                                                "role": "assistant"
                                            },
                                            "index": 0
                                        }
                                    ],
                                    "created": int(time.time()),
                                    "id": f"llm-{index}-{hash(accumulated_content)}",
                                    "model": model,
                                    "service_tier": "default",
                                    "object": "chat.completion.chunk",
                                    "usage": None
                                }

                                yield f"data: {json.dumps(unified_response, ensure_ascii=False)}\n\n"

                except (json.JSONDecodeError, KeyError) as e:
                    logger.warning(f"解析流式数据失败 (文本 {index}): {str(e)}, chunk: {chunk}")
                    continue


            logger.info(f"文本 {index} 翻译完成，最终长度: {len(accumulated_content)}")

        except Exception as e:
            logger.error(f"单个文本流式翻译失败 (文本 {index}): {str(e)}", exc_info=True)
            error_response = {
                "delta": {
                    "index": index,
                    "sourceText": text,
                    "error": True,
                    "message": f"翻译异常: {str(e)}"
                }
            }
            yield f"data: {json.dumps(error_response, ensure_ascii=False)}\n\n"

    def _extract_translation(self, response: Any) -> str:
        """从LLM响应中提取翻译文本"""
        try:
            if isinstance(response, dict):
                choices = response.get("choices", [])
                if choices:
                    message = choices[0].get("message", {})
                    content = message.get("content", "")
                    if isinstance(content, str):
                        return content.strip()
            elif isinstance(response, str):
                return response.strip()
        except Exception as e:
            logger.error(f"提取翻译结果失败: {str(e)}")
        
        return ""


class TranslationService:
    """翻译服务管理器"""

    def __init__(self):
        self.doubao_client = DoubaoTranslationClient()
        self.llm_client = LLMTranslationClient()
        logger.info("初始化翻译服务")

    async def translate(self, request: TranslationRequest) -> TranslationResult:
        """执行翻译"""
        # 检测输入格式但不转换
        is_string_input = isinstance(request.question, str)

        translate_options = request.translate_options or {}
        provider = translate_options.get("provider", "doubao")

        logger.info(f"=== TranslationService.translate 调用 ===")
        logger.info(f"Provider: {provider}")
        logger.info(f"Stream: {request.stream}")
        logger.info(f"Input format: {'string' if is_string_input else 'array'}")

        try:
            if provider == TranslationProvider.DOUBAO:
                # Doubao提供商：始终使用非流式翻译
                result = await self.doubao_client.translate(request)
            elif provider == TranslationProvider.LLM_TRANSLATE:
                # LLM提供商 + 非流式模式
                result = await self.llm_client.translate(request)
            else:
                # 默认使用LLM
                logger.warning(f"未知的翻译提供商: {provider}，使用默认的LLM")
                result = await self.llm_client.translate(request)

            logger.info(f"TranslationService.translate 返回结果: code={result.code}")
            return result

        except Exception as e:
            logger.error(f"翻译服务执行异常: {str(e)}", exc_info=True)
            return TranslationResult(
                code="error",
                message=f"翻译服务异常: {str(e)}",
                data=[]
            )

    async def stream_translate(self, request: TranslationRequest) -> AsyncGenerator[str, None]:
        """流式翻译"""
        # 检测输入格式但不转换
        is_string_input = isinstance(request.question, str)

        translate_options = request.translate_options or {}
        provider = translate_options.get("provider", "llm_translate")

        logger.info(f"=== TranslationService.stream_translate 调用 ===")
        logger.info(f"Provider: {provider}")
        logger.info(f"Input format: {'string' if is_string_input else 'array'}")

        try:
            # 判断提供商类型和流式支持
            if provider == TranslationProvider.DOUBAO:
                # Doubao提供商：使用统一的流式输出格式
                logger.info("Doubao提供商流式翻译，使用统一格式")
                result = await self.doubao_client.translate(request)
                logger.info(f"doubao translate result: {result}")

                # 将 doubao 的结果转换为统一的流式格式
                if result.code == "success" and result.data:
                    for item in result.data:
                        # 根据输入格式调整content输出
                        if is_string_input:
                            # 字符串输入：直接输出翻译文本
                            content = item["text"]
                        else:
                            # 数组输入：保持JSON格式
                            content = json.dumps(item, ensure_ascii=False)

                        # 构建统一的流式响应格式
                        unified_response = {
                            "choices": [
                                {
                                    "delta": {
                                        "content": content,
                                        "role": "assistant"
                                    },
                                    "index": 0
                                }
                            ],
                            "created": int(time.time()),
                            "id": f"doubao-{hash(str(item))}",
                            "model": "doubao",
                            "service_tier": "default",
                            "object": "chat.completion.chunk",
                            "usage": None
                        }
                        yield f"data: {json.dumps(unified_response, ensure_ascii=False)}\n\n"

                yield "data: [DONE]\n\n"

            elif provider == TranslationProvider.LLM_TRANSLATE:
                # LLM提供商：使用统一的流式输出格式
                logger.info("LLM提供商流式翻译，使用统一格式")
                async for chunk in self.llm_client.stream_translate(request):
                    yield chunk
            else:
                # 未提供的供应商，默认使用llm
                async for chunk in self.llm_client.stream_translate(request):
                    yield chunk

        except Exception as e:
            logger.error(f"流式翻译服务执行异常: {str(e)}", exc_info=True)
            error_data = {
                "code": "error",
                "message": f"流式翻译异常: {str(e)}",
                "data": []
            }

            error_response = {
                "choices": [
                    {
                        "delta": {
                            "content": json.dumps(error_data, ensure_ascii=False),
                            "role": "assistant"
                        },
                        "index": 0
                    }
                ],
                "created": int(time.time()),
                "id": f"service-error-{hash(str(e))}",
                "model": "translation-service",
                "service_tier": "default",
                "object": "chat.completion.chunk",
                "usage": None
            }
            yield f"data: {json.dumps(error_response, ensure_ascii=False)}\n\n"
            yield "data: [DONE]\n\n"

    async def close(self):
        """关闭服务"""
        await self.doubao_client.close()


# 全局翻译服务实例
translation_service = TranslationService()
